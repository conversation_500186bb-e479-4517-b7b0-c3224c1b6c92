<?php

// ======================================================
// css
// ======================================================
function style_script()
{

  wp_enqueue_style('common_css', get_template_directory_uri() . '/assets/css/common.css');

  if (is_front_page()) {
    wp_enqueue_style('top_css', get_template_directory_uri() . '/assets/css/top.css');
  } else {
    wp_enqueue_style('sub_css', get_template_directory_uri() . '/assets/css/sub.css');
  }

  if (is_singular('glossary') || is_archive('glossary')) {
    wp_enqueue_style('glossary', get_template_directory_uri() . '/assets/css/glossary.css');
  }

  wp_enqueue_style('sp_css', get_template_directory_uri() . '/assets/css/sp.css');

  if (is_page('movie')) {
    wp_enqueue_style('slick_css', get_template_directory_uri() . '/assets/slick/slick.css');
    wp_enqueue_style('slick_theme_css', get_template_directory_uri() . '/assets/slick/slick-theme.css');
    wp_enqueue_style('popup_css', get_template_directory_uri() . '/assets/magnific-popup/magnific-popup.css');
  }

  if (is_singular('webinar')) {
    wp_enqueue_style('webinar_css', get_template_directory_uri() . '/assets/css/webinar.css');
  }

  // オウンドメディアLPページ用のCSS
  if (is_page_template('s-owned-media.php')) {
    wp_enqueue_style('owned_media_css', get_template_directory_uri() . '/assets/css/owned-media.css', array(), '1.0.2');
  }
}
add_action('wp_enqueue_scripts', 'style_script');


/* ==========================================================================
favicon
========================================================================== */
function set_myfavicon()
{
  echo '<link rel="shortcut icon" href="' . get_template_directory_uri() . '/assets/images/logo/favicon.png">' . "\n";
  echo '<link rel="shortcut icon" type="image/x-icon" href="' . get_template_directory_uri() . '/assets/images/logo/favicon.ico" />';
  echo '<link rel="apple-touch-icon" href="' . get_template_directory_uri() . '/assets/images/logo/favicon.png">' . "\n";
}
add_action('wp_head', 'set_myfavicon');


/* ==========================================================================
Google Fonts
========================================================================== */
function google_font_scripts()
{
  echo '<link rel="preconnect" href="https://fonts.gstatic.com">' . "\n";
  echo '<link rel="preload" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700;800&display=swap" as="style">' . "\n";
  // wp_enqueue_style( 'google-font-noto', 'https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700;800&display=swap' );
}
add_action('wp_enqueue_scripts', 'google_font_scripts');


// ======================================================
// script
// ======================================================
function footer_script()
{

  wp_enqueue_script('jquery', 'https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js');
  wp_enqueue_script('main_js', get_template_directory_uri() . '/assets/js/main.js', array(), '', true);

  if (is_page('movie')) {
    wp_enqueue_script('slick_js', get_template_directory_uri() . '/assets/slick/slick.min.js', array(), '', true);
    wp_enqueue_script('popup_js', get_template_directory_uri() . '/assets/magnific-popup/jquery.magnific-popup.min.js', array(), '', true);
    wp_enqueue_script('movie_js', get_template_directory_uri() . '/assets/js/movie.js', array(), '', true);
  }
}
add_action('wp_enqueue_scripts', 'footer_script');


// ======================================================
// thumbnail activate
// ======================================================
add_theme_support('post-thumbnails');

// ======================================================
// カスタム投稿タイプ(ニュース)作成・設定
// ======================================================
require_once get_template_directory() . '/custom-news.php';



/* ==========================================================================
アーカイブタイトル 非表示
========================================================================== */
add_filter('get_the_archive_title', function ($title) {
  if (is_category()) {
    $title = single_cat_title('', false);
  } elseif (is_tag()) {
    $title = single_tag_title('', false);
  } elseif (is_tax()) {
    $title = single_term_title('', false);
  } elseif (is_post_type_archive()) {
    $title = post_type_archive_title('', false);
  } elseif (is_date()) {
    $title = get_the_time('Y年n月');
  } elseif (is_search()) {
    $title = '検索結果：' . esc_html(get_search_query(false));
  } elseif (is_404()) {
    $title = '「404」ページが見つかりません';
  } else {
  }
  return $title;
});



/* ==========================================================================
<body>に固定ページのスラッグ名追加
========================================================================== */
function pageslug_class($classes = '')
{
  if (is_page()) {
    $page = get_post(get_the_ID());
    $classes[] = $page->post_name;
    if ($page->post_parent) {
      $classes[] = get_page_uri($page->post_parent) . '-' . $page->post_name;
    }
  }
  return $classes;
}
add_filter('body_class', 'pageslug_class');



/* ==========================================================================
pタグ 調整
========================================================================== */
function filter_ptags_on_images($content)
{
  return preg_replace('/<p>\s*(<a .*>)?\s*(<img .* \/>)\s*(<\/a>)?\s*<\/p>/iU', '\1\2\3', $content);
}
add_filter('the_content', 'filter_ptags_on_images');

/* ==========================================================================
カスタム投稿タイプの記事編集画面にメタボックス（作成者変更）を表示
========================================================================== */
add_action('admin_menu', 'myplugin_add_custom_box');

function myplugin_add_custom_box()
{
  if (function_exists('add_meta_box')) {
    add_meta_box('myplugin_sectionid', __('作成者', 'myplugin_textdomain'), 'post_author_meta_box', 'blog', 'advanced');
  }
}

// カスタム投稿タイプの記事一覧に投稿者の項目を追加する
function manage_author_columns($columns)
{
  $columns['author'] = '作成者';
  return $columns;
}

function add_author_column($column, $post_id)
{
  if ('author' == $column) {
    $value = get_the_term_list($post_id, 'author');
    echo attribute_escape($value);
  }
}

add_filter('manage_posts_columns', 'manage_author_columns');
add_action('manage_posts_custom_column', 'add_author_column', 10, 2);

/* ==========================================================================
Widget定義
========================================================================== */
//管理画面をクラシック表示に変更
function widget_classic_view()
{
  remove_theme_support('widgets-block-editor');
}
add_action('after_setup_theme', 'widget_classic_view');

//widget有効化
function app_widgets_init()
{
  //サイドバー
  register_sidebar(array(
    'name' => 'Main Sidebar',
    'id' => 'main-sidebar',
    'description' => 'メインのウィジェット',
    'before_widget' => '<li>',
    'after_widget' => '</li>',
    'before_title' => 'content:none'

  ));

  //blog本文内
  //上
  register_sidebar(array(
    'name' => 'BLOG-widget-1(上)',
    'id' => 'blog-widget-1',
    'description' => '記事上：目次下',
    // 'before_widget' => '<div id="widget-1" class=""> <ul class="">',
    // 'after_widget' => '</ul></div>',
    // 'before_widget' => '<li style="list-style-type:none;margin:0 auto">',
    // 'after_widget' => '</li>',
    'before_widget' => '<div class="blog-widget__ele">',
    'after_widget' => '</div>',
    'before_title' => 'content:none'
  ));

  //中
  register_sidebar(array(
    'name' => 'BLOG-widget-2(中)',
    'id' => 'blog-widget-2',
    'description' => '記事中:2個目のh2の上',
    // 'before_widget' => '<div id="widget-2" class=""> <ul class="">',
    // 'after_widget' => '</ul></div>',
    // 'before_widget' => '<li style="list-style-type:none;margin:0 auto">',
    // 'after_widget' => '</li>',
    'before_widget' => '<div class="blog-widget__ele">',
    'after_widget' => '</div>',
    'before_title' => 'content:none'
  ));

  //下
  register_sidebar(array(
    'name' => 'BLOG-widget-3(下)',
    'id' => 'blog-widget-3',
    'description' => '記事下：まとめの下　記事をシェアの上',
    // 'before_widget' => '<div id="widget-3" class=""> <ul class="">',
    // 'after_widget' => '</ul></div>',
    // 'before_widget' => '<li style="list-style-type:none;margin:0 auto">',
    // 'after_widget' => '</li>',
    'before_widget' => '<div class="blog-widget__ele">',
    'after_widget' => '</div>',
    'before_title' => 'content:none'
  ));
}
add_action('widgets_init', 'app_widgets_init');

//widgetタイトル非表示
function remove_widget_title_all($widget_title)
{
  return;
}
add_filter('widget_title', 'remove_widget_title_all');

//不要なウィジェットを非表示
function my_widgets_init_cotrol()
{
  unregister_widget('WP_Widget_Pages'); //固定ページ
  unregister_widget('WP_Widget_Calendar'); //カレンダー
  unregister_widget('WP_Widget_Archives'); //アーカイブ
  unregister_widget('WP_Widget_Media_Audio'); //音声
  // unregister_widget('WP_Widget_Media_Image');//画像
  unregister_widget('WP_Widget_Media_Gallery'); //ギャラリー
  unregister_widget('WP_Widget_Media_Video'); //動画
  unregister_widget('WP_Widget_Meta'); //メタ情報
  unregister_widget('WP_Widget_Search'); //検索
  unregister_widget('WP_Widget_Text'); //テキスト
  unregister_widget('WP_Widget_Categories'); //カテゴリー
  unregister_widget('WP_Widget_Recent_Posts'); //最近の投稿
  unregister_widget('WP_Widget_Recent_Comments'); //最近のコメント
  unregister_widget('WP_Widget_RSS'); //RSS
  unregister_widget('WP_Widget_Tag_Cloud'); //タグクラウド
  unregister_widget('WP_Nav_Menu_Widget'); //ナビゲーションメニュー
  unregister_widget('WP_User_Avatar_Profile_Widget'); //WP User Avatar
  unregister_widget('bcn_widget'); //Breadcrumb(パンくず)
  unregister_widget('toc_widget'); //TOC(目次)
  unregister_widget('WP_Widget_Block'); //Block
}
add_action('widgets_init', 'my_widgets_init_cotrol', 11);

// ======================================================
// カスタム投稿タイプ(導入事例)作成・設定
// ======================================================
// カスタム投稿作成
register_post_type(
  'casestudy',
  array(
    'label' => '導入事例',
    'hierarchical' => false,
    'public' => true,
    'supports' => array('title', 'editor', 'excerpt', 'custom-fields', 'revisions', 'thumbnail'),
    'has_archive' => true,
  )
);

// カスタム投稿のカテゴリ作成
register_taxonomy(
  'casestudy-cat',
  'casestudy',
  [
    'label' => 'カテゴリー',
    'public' => true,
    'hierarchical' => true
  ]
);

// カテゴリボタン表示設定
function add_site_settings_menu()
{
  add_submenu_page('edit.php?post_type=casestudy', '表示設定', '表示設定', 'manage_options', 'site_settings.php', 'site_settings_page');
}
add_action('admin_menu', 'add_site_settings_menu');

function settings_page()
{
  add_settings_section("section", "一覧カテゴリボタン表示設定", null, "checkbox");
  add_settings_field("dispCheck", "表示の場合はチェック", "checkbox_display", "checkbox", "section");
  register_setting("section", "dispCheck");
}

function checkbox_display()
{
?>
  <input type="checkbox" name="dispCheck" value="1"
    <?php checked(1, get_option('dispCheck'), true); ?> />
<?php
}
add_action("admin_init", "settings_page");

function site_settings_page()
{
?>
  <div class="wrap">
    <form method="post" action="options.php">
      <?php
      settings_fields("section");
      do_settings_sections("checkbox");
      submit_button();
      ?>
    </form>
  </div>
  <?php
}


// ======================================================
// カスタム投稿タイプ(ウェビナー)作成・設定
// ======================================================
// カスタム投稿作成
register_post_type(
  'webinar',
  array(
    'label' => 'ウェビナー',
    'hierarchical' => false,
    'public' => true,
    'supports' => array('title', 'excerpt', 'custom-fields', 'revisions'),
    'has_archive' => true,
  )
);
// カスタム投稿のカテゴリ作成
register_taxonomy(
  'webinar-cat',
  'webinar',
  [
    'label' => 'カテゴリー',
    'public' => true,
    'hierarchical' => true
  ]
);
add_action('init', function () {
  remove_post_type_support('webinar', 'editor');
}, 99);
function webinarSlug($slug, $post_ID, $post_status, $post_type)
{
  $post = get_post($post_ID);

  $webinar = get_field('webinar');
  if ($post_type == 'webinar') {
    if (get_post_status() !== 'publish') {
      $slug = $webinar['date'];
    }

    if (!has_post_thumbnail()) {
      update_post_meta($post_ID, '_thumbnail_id', attachment_url_to_postid($webinar['pc']));
    }
  }
  return $slug;
}
add_filter('wp_unique_post_slug', 'webinarSlug', 10, 4);


// サービスページスライドショー読込み
function get_slide_images($dir)
{
  $images = array();
  $supported_formats = array('jpg', 'jpeg', 'png', 'gif', 'webp');

  if (is_dir($dir)) {
    $files = scandir($dir);
    foreach ($files as $file) {
      $file_ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
      if (in_array($file_ext, $supported_formats)) {
        $images[] = $dir . '/' . $file;
      }
    }
  }
  return $images;
}

// 画像区分判定
function is_slide_image($filename, $pattern)
{
  return preg_match($pattern, $filename) === 1;
}

// webp対応判定
function supports_webp()
{
  return strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false;
}
// 画像拡張子
$image_extension = supports_webp() ? '.webp' : '.png';


// ツールチップ(用語集)関連インポート
require_once('functions/custom-glossary.php');


// noindex
// カラムの追加（blogタイプのみ）
add_filter('manage_blog_posts_columns', function ($columns) {
  $columns['column_noindex'] = 'noindex付与';
  $columns['column_nofollow'] = 'nofollow付与';
  return $columns;
});

// カラムの内容表示（blogタイプのみ）
add_action('manage_blog_posts_custom_column', function ($column_name, $post_id) {
  switch ($column_name) {
    case 'column_noindex':
      echo get_field('column_noindex', $post_id) ? '✓' : '－';
      break;
    case 'column_nofollow':
      echo get_field('column_nofollow', $post_id) ? '✓' : '－';
      break;
  }
}, 10, 2);

// クイック編集フィールドの追加（blogタイプのみ）
add_action('quick_edit_custom_box', function ($column_name, $post_type) {
  if ($post_type !== 'blog') {
    return;
  }

  switch ($column_name) {
    case 'column_noindex':
  ?>
      <fieldset class="inline-edit-col-right">
        <div class="inline-edit-col">
          <label class="alignleft">
            <input type="checkbox" name="column_noindex" value="1">
            <span class="checkbox-title">noindex付与</span>
          </label>
        </div>
      </fieldset>
    <?php
      break;
    case 'column_nofollow':
    ?>
      <fieldset class="inline-edit-col-right">
        <div class="inline-edit-col">
          <label class="alignleft">
            <input type="checkbox" name="column_nofollow" value="1">
            <span class="checkbox-title">nofollow付与</span>
          </label>
        </div>
      </fieldset>
  <?php
      break;
  }
}, 10, 2);

// 保存処理（blogタイプのみ）
add_action('save_post_blog', function ($post_id) {
  if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
    return;
  }
  if (!current_user_can('edit_post', $post_id)) {
    return;
  }

  // noindex
  $noindex = isset($_REQUEST['column_noindex']) ? 1 : 0;
  update_field('column_noindex', $noindex, $post_id);

  // nofollow
  $nofollow = isset($_REQUEST['column_nofollow']) ? 1 : 0;
  update_field('column_nofollow', $nofollow, $post_id);
});

// クイック編集時に現在の値を設定するJavaScript
add_action('admin_footer', function () {
  // 現在の画面がblogの一覧画面かチェック
  $screen = get_current_screen();
  if ($screen->post_type !== 'blog') {
    return;
  }
  ?>
  <script type="text/javascript">
    jQuery(document).ready(function($) {
      var wp_inline_edit = inlineEditPost.edit;
      inlineEditPost.edit = function(id) {
        wp_inline_edit.apply(this, arguments);
        var post_id = 0;
        if (typeof(id) == 'object') {
          post_id = parseInt(this.getId(id));
        }
        if (post_id > 0) {
          var edit_row = $('#edit-' + post_id);
          var post_row = $('#post-' + post_id);
          // noindexの値を設定
          var noindex_value = $('.column-column_noindex', post_row).text().trim() === '✓';
          $(':input[name="column_noindex"]', edit_row).prop('checked', noindex_value);
          // nofollowの値を設定
          var nofollow_value = $('.column-column_nofollow', post_row).text().trim() === '✓';
          $(':input[name="column_nofollow"]', edit_row).prop('checked', nofollow_value);
        }
      };
    });
  </script>
<?php
});
?>